"""
Test script to verify the updated constants and order query service functionality.
"""

import sys
import os

# Add the application directory to Python path
sys.path.append('/Users/<USER>/Desktop/p/rozana-oms-service/application')

from app.core.constants import OrderStatus, PaymentStatus, SystemConstants, APIConstants

def test_constants():
    """Test the updated constants"""
    print("Testing Constants...")
    
    # Test OrderStatus
    print(f"OrderStatus.OPEN: {OrderStatus.OPEN}")
    print(f"Customer status name for OPEN: {OrderStatus.get_customer_status_name(OrderStatus.OPEN)}")
    print(f"Internal status name for OPEN: {OrderStatus.get_status_name(OrderStatus.OPEN)}")
    
    # Test pre-computed status sets
    print(f"Is OPEN a Rozana status? {OrderStatus.is_rozana_status(OrderStatus.OPEN)}")
    print(f"Is WMS_SYNCED a WMS status? {OrderStatus.is_wms_status(OrderStatus.WMS_SYNCED)}")
    print(f"Is TMS_SYNCED a TMS status? {OrderStatus.is_tms_status(OrderStatus.TMS_SYNCED)}")
    
    # Test PaymentStatus
    print(f"PaymentStatus.COMPLETED: {PaymentStatus.COMPLETED}")
    print(f"Payment description for COMPLETED: {PaymentStatus.get_description(PaymentStatus.COMPLETED)}")
    
    # Test that oms_return is in DB_STATUS_MAP
    print(f"'oms_return' in DB_STATUS_MAP: {'oms_return' in OrderStatus.DB_STATUS_MAP}")
    print(f"DB_STATUS_MAP['oms_return']: {OrderStatus.DB_STATUS_MAP.get('oms_return', 'NOT FOUND')}")
    
    print("Constants test completed successfully!\n")

def test_order_query_service():
    """Test the order query service (without database connection)"""
    print("Testing OrderQueryService structure...")
    
    from app.services.order_query_service import OrderQueryService, get_rozana_status_codes_for_sql
    
    # Test helper function
    rozana_codes = get_rozana_status_codes_for_sql()
    print(f"Rozana status codes for SQL: {rozana_codes}")
    
    # Test service instantiation
    service = OrderQueryService()
    print(f"OrderQueryService instantiated: {service is not None}")
    
    print("OrderQueryService test completed successfully!\n")

def test_optimized_order_query_service():
    """Test the optimized order query service"""
    print("Testing OptimizedOrderQueryService structure...")
    
    from app.services.optimized_order_query_service import OptimizedOrderQueryService, OrderQueryOptimizer
    
    # Test optimizer utilities
    optimizer = OrderQueryOptimizer()
    print(f"OrderQueryOptimizer instantiated: {optimizer is not None}")
    
    # Test safe float conversion
    print(f"Safe float convert None: {optimizer.safe_float_convert(None)}")
    print(f"Safe float convert '123.45': {optimizer.safe_float_convert('123.45')}")
    print(f"Safe float convert 'invalid': {optimizer.safe_float_convert('invalid')}")
    
    # Test cached sort clause
    sort_desc = optimizer.get_order_sort_clause("desc")
    sort_asc = optimizer.get_order_sort_clause("asc")
    print(f"Sort clause DESC: {sort_desc}")
    print(f"Sort clause ASC: {sort_asc}")
    
    # Test service instantiation
    service = OptimizedOrderQueryService()
    print(f"OptimizedOrderQueryService instantiated: {service is not None}")
    
    print("OptimizedOrderQueryService test completed successfully!\n")

if __name__ == "__main__":
    print("Running validation tests for updated order management services...\n")
    
    try:
        test_constants()
        test_order_query_service()
        test_optimized_order_query_service()
        
        print("✅ All tests passed! The implementation is working correctly.")
        
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
