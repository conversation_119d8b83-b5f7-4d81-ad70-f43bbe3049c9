"""add_typesense_fields_to_order_items

Revision ID: ae_add_typesense_fields
Revises: ae_add_coordinates
Create Date: 2025-08-08 01:46:00.000000

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'ae_add_typesense_fields'
down_revision: Union[str, Sequence[str], None] = 'ae_add_coordinates'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Add Typesense-sourced fields to order_items table."""
    # Add tax fields
    op.add_column('order_items', sa.Column('cgst', sa.DECIMAL(precision=10, scale=2), nullable=False, server_default='0.00'))
    op.add_column('order_items', sa.Column('sgst', sa.DECIMAL(precision=10, scale=2), nullable=False, server_default='0.00'))
    op.add_column('order_items', sa.Column('igst', sa.DECIMAL(precision=10, scale=2), nullable=False, server_default='0.00'))
    op.add_column('order_items', sa.Column('cess', sa.DECIMAL(precision=10, scale=2), nullable=False, server_default='0.00'))
    
    # Add return policy fields
    op.add_column('order_items', sa.Column('is_returnable', sa.Boolean(), nullable=False, server_default='false'))
    op.add_column('order_items', sa.Column('return_type', sa.String(length=2), nullable=False, server_default='00'))
    op.add_column('order_items', sa.Column('return_window', sa.Integer(), nullable=False, server_default='0'))
    
    # Add selling price net field
    op.add_column('order_items', sa.Column('selling_price_net', sa.DECIMAL(precision=10, scale=2), nullable=False, server_default='0.00'))


def downgrade() -> None:
    """Remove Typesense-sourced fields from order_items table."""
    op.drop_column('order_items', 'selling_price_net')
    op.drop_column('order_items', 'return_window')
    op.drop_column('order_items', 'return_type')
    op.drop_column('order_items', 'is_returnable')
    op.drop_column('order_items', 'cess')
    op.drop_column('order_items', 'igst')
    op.drop_column('order_items', 'sgst')
    op.drop_column('order_items', 'cgst')
