"""add_name_field_to_order_items_and_utr_to_payments

Revision ID: c390c8d977ae
Revises: ae_add_typesense_fields
Create Date: 2025-08-11 13:32:11.180168

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'c390c8d977ae'
down_revision: Union[str, Sequence[str], None] = 'ae_add_typesense_fields'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # Add name field to order_items table (optional field)
    op.add_column('order_items', sa.Column('name', sa.String(length=200), nullable=True))
    
    # Add utr_number field to payment_details table (optional field)
    op.add_column('payment_details', sa.Column('utr_number', sa.String(length=50), nullable=True))


def downgrade() -> None:
    """Downgrade schema."""
    # Remove the added fields in reverse order
    op.drop_column('payment_details', 'utr_number')
    op.drop_column('order_items', 'name')
