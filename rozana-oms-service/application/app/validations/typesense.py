import os
import logging
from typing import Dict, List
from fastapi import HTTPException
from app.services.typesense_service import TypesenseService
from app.dto.orders import OrderItemCreate

logger = logging.getLogger(__name__)

PRICE_CHECK_ENABLED = os.getenv("PRICE_CHECK_ENABLED", "true").lower() == "true"


class TypesenseValidator:
    def __init__(self):
        self.typesense_service = TypesenseService()
    
    async def validate_and_enrich_items(self, items: List[OrderItemCreate], facility_name: str) -> List[Dict]:

        enriched_items = []
        
        for item in items:
            try:
                product = await self.typesense_service.get_product_by_sku(item.sku, facility_name)
                
                if not product:
                    raise ValueError(f"Stock not available for facility {facility_name} and sku {item.sku}")
                
                if PRICE_CHECK_ENABLED:
                    price_valid = await self.typesense_service.validate_product_price(
                        item.sku, facility_name, item.sale_price
                    )
                    
                    if not price_valid:
                        typesense_price = product.get("selling_price") or product.get("price")
                        raise HTTPException(
                            status_code=400,
                            detail=f"Price mismatch for SKU {item.sku}. "
                                   f"Expected: {typesense_price}, Received: {item.sale_price}"
                        )
                typesense_fields = self.typesense_service.extract_item_fields(product)
                enriched_item = {
                    "sku": item.sku,
                    "quantity": item.quantity,
                    "unit_price": item.unit_price,
                    "sale_price": item.sale_price,
                    **typesense_fields
                }
                
                enriched_items.append(enriched_item)
                logger.info(f"Successfully enriched item data for SKU: {item.sku}")
                
            except HTTPException:
                raise
            except Exception as e:
                logger.error(f"Error processing item {item.sku}: {str(e)}")
                raise HTTPException(
                    status_code=500,
                    detail=f"Error processing item {item.sku}: {str(e)}"
                )
        
        return enriched_items
    
    async def validate_single_item(self, sku: str, facility_name: str, sale_price: float) -> Dict:
        try:
            product = await self.typesense_service.get_product_by_sku(sku, facility_name)
            
            if not product:
                raise ValueError(f"Stock not available for facility {facility_name} and sku {sku}")
            
            if PRICE_CHECK_ENABLED:
                price_valid = await self.typesense_service.validate_product_price(sku, facility_name, sale_price)
                
                if not price_valid:
                    typesense_price = product.get("selling_price") or product.get("price")
                    raise HTTPException(
                        status_code=400,
                        detail=f"Price mismatch for SKU {sku}. "
                               f"Expected: {typesense_price}, Received: {sale_price}"
                    )
            
            return self.typesense_service.extract_item_fields(product)
            
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error validating item {sku}: {str(e)}")
            raise HTTPException(
                status_code=500,
                detail=f"Error validating item {sku}: {str(e)}"
            )
