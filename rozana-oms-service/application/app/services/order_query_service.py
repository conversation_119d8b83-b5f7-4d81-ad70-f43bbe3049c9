from typing import Dict, Optional, List
import logging
from app.core.constants import PaymentStatus, OrderStatus, ReturnTypeConstants
from app.connections.database import execute_raw_sql_readonly

logger = logging.getLogger(__name__)

class OrderQueryService:
    """Service for handling order queries (Read operations) using SQLAlchemy raw SQL"""

    def __init__(self, db_conn=None):
        pass

    def _get_return_type_description(self, return_type_code: str) -> str:
        """Convert return_type code to description using constants"""
        return ReturnTypeConstants.get_description(return_type_code)

    def get_order_by_id(self, order_id: str) -> Optional[Dict]:
        """Get single order by order_id from database with complete details"""
        
        try:
            sql = """
                SELECT o.id, o.order_id, o.customer_id, o.customer_name,
                       o.facility_id, o.facility_name,
                       o.status, o.total_amount, o.eta,
                       o.created_at, o.updated_at,
                       oi.id as item_id, oi.sku, oi.name, oi.quantity, oi.unit_price, oi.sale_price, 
                       oi.status AS item_status, oi.cgst, oi.sgst, oi.igst, oi.cess, 
                       oi.is_returnable, oi.return_type, oi.return_window, oi.created_at as item_created_at, 
                       oi.updated_at as item_updated_at,
                       oa.full_name, oa.phone_number, oa.address_line1, oa.address_line2,
                       oa.city, oa.state, oa.postal_code, oa.country, oa.type_of_address,
                       oa.longitude, oa.latitude
                FROM orders o
                LEFT JOIN order_items oi ON oi.order_id = o.id
                LEFT JOIN order_addresses oa ON oa.order_id = o.id
                WHERE o.order_id = :order_id
            """
            
            rows = execute_raw_sql_readonly(sql, {'order_id': order_id})
            if not rows:
                return None

            header = rows[0]
            address = None
            items = []

            for record in rows:
                if address is None and record.get("full_name"):
                    address = {
                        "full_name": record.get("full_name"),
                        "phone_number": record.get("phone_number"),
                        "address_line1": record.get("address_line1"),
                        "address_line2": record.get("address_line2"),
                        "city": record.get("city"),
                        "state": record.get("state"),
                        "postal_code": record.get("postal_code"),
                        "country": record.get("country"),
                        "type_of_address": record.get("type_of_address"),
                        "longitude": float(record.get("longitude")) if record.get("longitude") is not None else None,
                        "latitude": float(record.get("latitude")) if record.get("latitude") is not None else None
                    }

                if record.get("sku"):
                    # Get return type interpretation
                    return_type_code = record.get("return_type", "00")
                    return_type_description = self._get_return_type_description(return_type_code)
                    
                    items.append({
                        "id": record.get("item_id"),
                        "order_id": header.get("id"),
                        "sku": record.get("sku"),
                        "name": record.get("name"),
                        "quantity": record.get("quantity"),
                        "unit_price": float(record.get("unit_price", 0)),
                        "sale_price": float(record.get("sale_price", 0)),
                        "status": OrderStatus.get_customer_status_name(record.get("item_status")),
                        "created_at": record.get("item_created_at"),
                        "updated_at": record.get("item_updated_at"),
                        "cgst": float(record.get("cgst", 0)),
                        "sgst": float(record.get("sgst", 0)),
                        "igst": float(record.get("igst", 0)),
                        "cess": float(record.get("cess", 0)),
                        "is_returnable": record.get("is_returnable", True),
                        "return_type": return_type_description,
                        "return_window": record.get("return_window", 7)
                    })

            # Get payment details
            payment_sql = """
                SELECT pd.id, pd.payment_id, pd.payment_amount, pd.payment_date, pd.payment_mode, 
                       pd.cash_amount, pd.online_amount, pd.total_amount, pd.payment_status, 
                       pd.utr_number, pd.created_at, pd.updated_at
                FROM payment_details pd
                JOIN orders o ON o.id = pd.order_id
                WHERE o.order_id = :order_id
                ORDER BY pd.created_at DESC
            """

            payment_rows = execute_raw_sql_readonly(payment_sql, {"order_id": order_id})
            payments = []
            for payment_row in payment_rows:
                payments.append({
                    "payment_id": payment_row.get("payment_id"),
                    "payment_amount": float(payment_row.get("payment_amount", 0)),
                    "payment_date": payment_row.get("payment_date"),
                    "payment_mode": payment_row.get("payment_mode"),
                    "cash_amount": float(payment_row.get("cash_amount", 0)),
                    "online_amount": float(payment_row.get("online_amount", 0)),
                    "total_amount": float(payment_row.get("total_amount", 0)),
                    "payment_status": {
                        "code": payment_row.get("payment_status"),
                        "description": PaymentStatus.get_description(payment_row.get("payment_status"))
                    },
                    "utr_number": payment_row.get("utr_number"),
                    "created_at": payment_row.get("created_at"),
                    "updated_at": payment_row.get("updated_at")
                })

            return {
                "id": header.get("id"),
                "order_id": header.get("order_id"),
                "customer_id": header.get("customer_id"),
                "customer_name": header.get("customer_name"),
                "facility_id": header.get("facility_id"),
                "facility_name": header.get("facility_name"),
                "status": OrderStatus.get_customer_status_name(header.get("status")),
                "total_amount": float(header.get("total_amount", 0)),
                "eta": header.get("eta"),
                "created_at": header.get("created_at"),
                "updated_at": header.get("updated_at"),
                "address": address,
                "items": items,
                "payments": payments
            }

        except Exception as e:
            logger.error(f"Error fetching order {order_id}: {e}")
            raise

    def get_all_orders(self, user_id: str, limit: int = 20, offset: int = 0, sort_order: str = "desc") -> List[Dict]:
        """Get all orders for a user with pagination using SQLAlchemy raw SQL"""
        
        try:
            order_clause = "ORDER BY o.created_at DESC" if sort_order.lower() == "desc" else "ORDER BY o.created_at ASC"
            
            sql = f"""
                SELECT o.id, o.order_id, o.customer_id, o.customer_name,
                       o.facility_id, o.facility_name,
                       o.status, o.total_amount, o.eta,
                       o.created_at, o.updated_at
                FROM orders o
                WHERE o.customer_id = :user_id
                {order_clause}
                LIMIT :limit OFFSET :offset
            """
            
            rows = execute_raw_sql_readonly(sql, {
                'user_id': user_id,
                'limit': limit,
                'offset': offset
            })
            
            orders = []
            for row in rows:
                orders.append({
                    "id": row.get("id"),
                    "order_id": row.get("order_id"),
                    "customer_id": row.get("customer_id"),
                    "customer_name": row.get("customer_name"),
                    "facility_id": row.get("facility_id"),
                    "facility_name": row.get("facility_name"),
                    "status": OrderStatus.get_customer_status_name(row.get("status")),
                    "total_amount": float(row.get("total_amount", 0)),
                    "eta": row.get("eta"),
                    "created_at": row.get("created_at"),
                    "updated_at": row.get("updated_at")
                })
            
            return orders
            
        except Exception as e:
            logger.error(f"Error fetching orders for user {user_id}: {e}")
            raise

    def get_order_again_products(self, user_id: str, limit: int = 20, offset: int = 0):
        try:
            recent_sql = """
                SELECT 
                    oi.sku,
                    COUNT(*) AS order_count
                FROM orders o
                JOIN order_items oi 
                    ON oi.order_id = o.id
                WHERE o.customer_id = :user_id
                GROUP BY oi.sku
                ORDER BY order_count DESC
                LIMIT :limit OFFSET :offset
            """

            rows = execute_raw_sql_readonly(
                recent_sql, 
                {"user_id": user_id, "limit": limit, "offset": offset}
            )
            
            products = [row.get("sku") for row in rows if row.get("sku")]            
            return products

        except Exception as e:
            logger.error(f"Error fetching order-again products for user {user_id}: {e}")
            return []


    def get_all_facility_orders(self, facility_name: str, limit: int = 10, offset: int = 0, sort_order: str = "desc") -> List[Dict]:
        """Get all orders for a facility with pagination using SQLAlchemy raw SQL"""
        
        try:
            order_clause = "ORDER BY o.created_at DESC" if sort_order.lower() == "desc" else "ORDER BY o.created_at ASC"
            
            sql = f"""
                SELECT o.id, o.order_id, o.customer_id, o.customer_name,
                       o.facility_id, o.facility_name,
                       o.status, o.total_amount, o.eta,
                       o.created_at, o.updated_at
                FROM orders o
                WHERE o.facility_name = :facility_name
                {order_clause}
                LIMIT :limit OFFSET :offset
            """
            
            rows = execute_raw_sql_readonly(sql, {
                'facility_name': facility_name,
                'limit': limit,
                'offset': offset
            })
            
            orders = []
            for row in rows:
                orders.append({
                    "id": row.get("id"),
                    "order_id": row.get("order_id"),
                    "customer_id": row.get("customer_id"),
                    "customer_name": row.get("customer_name"),
                    "facility_id": row.get("facility_id"),
                    "facility_name": row.get("facility_name"),
                    "status": OrderStatus.get_customer_status_name(row.get("status")),
                    "total_amount": float(row.get("total_amount", 0)),
                    "eta": row.get("eta"),
                    "created_at": row.get("created_at"),
                    "updated_at": row.get("updated_at")
                })
            
            return orders
            
        except Exception as e:
            logger.error(f"Error fetching orders for facility {facility_name}: {e}")
            raise
    
    def get_orders_by_customer_id(self, customer_id: str, limit: int = 20, offset: int = 0, sort_order: str = "desc") -> List[Dict]:
        return self.get_all_orders(customer_id, limit, offset, sort_order)
    
    def get_order_items_by_customer_id(self, customer_id: str) -> List[Dict]:
        try:
            sql = """
                SELECT oi.id, oi.order_id, oi.sku, oi.name, oi.quantity, 
                       oi.unit_price, oi.sale_price, oi.status,
                       oi.cgst, oi.sgst, oi.igst, oi.cess, 
                       oi.is_returnable, oi.return_type, oi.return_window,
                       oi.created_at, oi.updated_at
                FROM order_items oi
                JOIN orders o ON o.id = oi.order_id
                WHERE o.customer_id = :customer_id
                ORDER BY oi.created_at DESC
            """
            
            rows = execute_raw_sql_readonly(sql, {'customer_id': customer_id})
            
            items = []
            for row in rows:
                return_type_description = self._get_return_type_description(row.get("return_type", "00"))
                
                items.append({
                    "id": row.get("id"),
                    "order_id": row.get("order_id"),
                    "sku": row.get("sku"),
                    "name": row.get("name"),
                    "quantity": row.get("quantity"),
                    "unit_price": float(row.get("unit_price", 0)),
                    "sale_price": float(row.get("sale_price", 0)),
                    "status": OrderStatus.get_customer_status_name(row.get("status")),
                    "created_at": row.get("created_at"),
                    "updated_at": row.get("updated_at"),
                    "cgst": float(row.get("cgst", 0)),
                    "sgst": float(row.get("sgst", 0)),
                    "igst": float(row.get("igst", 0)),
                    "cess": float(row.get("cess", 0)),
                    "is_returnable": row.get("is_returnable", True),
                    "return_type": return_type_description,
                    "return_window": row.get("return_window", 7)
                })
            
            return items
            
        except Exception as e:
            logger.error(f"Error fetching order items for customer {customer_id}: {e}")
            raise
    
    def get_order_items_by_order_id(self, order_id: str) -> List[Dict]:
        try:
            sql = """
                SELECT oi.id, oi.order_id, oi.sku, oi.name, oi.quantity, 
                       oi.unit_price, oi.sale_price, oi.status,
                       oi.cgst, oi.sgst, oi.igst, oi.cess, 
                       oi.is_returnable, oi.return_type, oi.return_window,
                       oi.created_at, oi.updated_at
                FROM order_items oi
                JOIN orders o ON o.id = oi.order_id
                WHERE o.order_id = :order_id
                ORDER BY oi.created_at DESC
            """
            
            rows = execute_raw_sql_readonly(sql, {'order_id': order_id})
            
            items = []
            for row in rows:
                return_type_description = self._get_return_type_description(row.get("return_type", "00"))
                
                items.append({
                    "id": row.get("id"),
                    "order_id": row.get("order_id"),
                    "sku": row.get("sku"),
                    "name": row.get("name"),
                    "quantity": row.get("quantity"),
                    "unit_price": float(row.get("unit_price", 0)),
                    "sale_price": float(row.get("sale_price", 0)),
                    "status": OrderStatus.get_customer_status_name(row.get("status")),
                    "created_at": row.get("created_at"),
                    "updated_at": row.get("updated_at"),
                    "cgst": float(row.get("cgst", 0)),
                    "sgst": float(row.get("sgst", 0)),
                    "igst": float(row.get("igst", 0)),
                    "cess": float(row.get("cess", 0)),
                    "is_returnable": row.get("is_returnable", True),
                    "return_type": return_type_description,
                    "return_window": row.get("return_window", 7)
                })
            
            return items
            
        except Exception as e:
            logger.error(f"Error fetching order items for order {order_id}: {e}")
            raise