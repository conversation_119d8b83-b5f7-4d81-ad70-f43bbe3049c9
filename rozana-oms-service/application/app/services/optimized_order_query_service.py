"""
Optimized Order Query Service with performance improvements and better code organization.

Key optimizations:
1. Pre-computed SQL fragments and constants
2. Cached sort clauses and common operations
3. Efficient data transformation utilities
4. Reduced database round trips
5. Better memory usage patterns
6. Type safety improvements
"""

from typing import Dict, Optional, List, Tuple, Any, Union
import logging
from decimal import Decimal
from functools import lru_cache
from app.core.constants import PaymentStatus, OrderStatus
from app.connections.database import execute_raw_sql_readonly

logger = logging.getLogger(__name__)

class OrderQueryOptimizer:
    """Optimization utilities for order queries"""
    
    # Pre-computed SQL fragments for better performance
    _ROZANA_STATUS_SQL = ', '.join(map(str, OrderStatus._ROZANA_STATUSES))
    _WMS_STATUS_SQL = ', '.join(map(str, OrderStatus._WMS_STATUSES))
    _TMS_STATUS_SQL = ', '.join(map(str, OrderStatus._TMS_STATUSES))
    
    # Common SQL fragments to avoid repetition
    ORDER_BASE_FIELDS = """
        o.id, o.order_id, o.customer_id, o.customer_name,
        o.facility_id, o.facility_name, o.status, o.total_amount, 
        o.eta, o.created_at, o.updated_at
    """
    
    ORDER_ITEM_FIELDS = """
        oi.id as item_id, oi.sku, oi.name, oi.quantity, 
        oi.unit_price, oi.sale_price, oi.status AS item_status,
        oi.cgst, oi.sgst, oi.igst, oi.cess, 
        oi.is_returnable, oi.return_type, oi.return_window,
        oi.selling_price_net, oi.created_at as item_created_at, 
        oi.updated_at as item_updated_at
    """
    
    ADDRESS_FIELDS = """
        oa.full_name, oa.phone_number, oa.address_line1, oa.address_line2,
        oa.city, oa.state, oa.postal_code, oa.country, oa.type_of_address,
        oa.longitude, oa.latitude
    """
    
    PAYMENT_FIELDS = """
        pd.payment_id, pd.payment_amount, pd.payment_mode, pd.payment_status,
        pd.cash_amount, pd.online_amount, pd.total_amount as payment_total,
        pd.utr_number, pd.created_at as payment_created_at, 
        pd.updated_at as payment_updated_at
    """
    
    @staticmethod
    @lru_cache(maxsize=128)
    def get_order_sort_clause(sort_order: str) -> str:
        """Get cached sort clause for orders"""
        return "ORDER BY o.created_at DESC" if sort_order.lower() == "desc" else "ORDER BY o.created_at ASC"
    
    @staticmethod
    def safe_float_convert(value: Any) -> float:
        """Safely convert value to float with proper handling"""
        if value is None:
            return 0.0
        if isinstance(value, (int, float)):
            return float(value)
        if isinstance(value, Decimal):
            return float(value)
        try:
            return float(value)
        except (ValueError, TypeError):
            return 0.0
    
    @staticmethod
    def build_address_dict(record: Dict) -> Optional[Dict]:
        """Build address dictionary from record with null checks"""
        if not record.get("full_name"):
            return None
        
        return {
            "full_name": record.get("full_name"),
            "phone_number": record.get("phone_number"),
            "address_line1": record.get("address_line1"),
            "address_line2": record.get("address_line2"),
            "city": record.get("city"),
            "state": record.get("state"),
            "postal_code": record.get("postal_code"),
            "country": record.get("country"),
            "type_of_address": record.get("type_of_address"),
            "longitude": OrderQueryOptimizer.safe_float_convert(record.get("longitude")),
            "latitude": OrderQueryOptimizer.safe_float_convert(record.get("latitude"))
        }
    
    @staticmethod
    def build_order_item_dict(record: Dict) -> Dict:
        """Build order item dictionary with optimized field access"""
        return {
            "id": record.get("item_id"),
            "sku": record.get("sku"),
            "name": record.get("name"),
            "quantity": record.get("quantity"),
            "unit_price": OrderQueryOptimizer.safe_float_convert(record.get("unit_price")),
            "sale_price": OrderQueryOptimizer.safe_float_convert(record.get("sale_price")),
            "selling_price_net": OrderQueryOptimizer.safe_float_convert(record.get("selling_price_net")),
            "status": OrderStatus.get_customer_status_name(record.get("item_status")),
            "cgst": OrderQueryOptimizer.safe_float_convert(record.get("cgst")),
            "sgst": OrderQueryOptimizer.safe_float_convert(record.get("sgst")),
            "igst": OrderQueryOptimizer.safe_float_convert(record.get("igst")),
            "cess": OrderQueryOptimizer.safe_float_convert(record.get("cess")),
            "is_returnable": record.get("is_returnable", False),
            "return_type": record.get("return_type", "00"),
            "return_window": record.get("return_window", 0),
            "created_at": record.get("item_created_at"),
            "updated_at": record.get("item_updated_at")
        }
    
    @staticmethod
    def build_payment_dict(record: Dict) -> Dict:
        """Build payment dictionary with optimized field access"""
        return {
            "payment_id": record.get("payment_id"),
            "payment_amount": OrderQueryOptimizer.safe_float_convert(record.get("payment_amount")),
            "payment_mode": record.get("payment_mode"),
            "payment_status": PaymentStatus.get_description(record.get("payment_status")),
            "cash_amount": OrderQueryOptimizer.safe_float_convert(record.get("cash_amount")),
            "online_amount": OrderQueryOptimizer.safe_float_convert(record.get("online_amount")),
            "total_amount": OrderQueryOptimizer.safe_float_convert(record.get("payment_total")),
            "utr_number": record.get("utr_number"),
            "created_at": record.get("payment_created_at"),
            "updated_at": record.get("payment_updated_at")
        }


class OptimizedOrderQueryService:
    """Optimized service for handling order queries with improved performance"""

    def __init__(self):
        self.optimizer = OrderQueryOptimizer()

    def get_order_by_id_optimized(self, order_id: str) -> Optional[Dict]:
        """
        Get single order by order_id with optimized single-query approach.
        
        Performance improvements:
        - Single query instead of multiple round trips
        - Pre-computed field selections
        - Optimized data transformation
        - Better memory usage
        """
        try:
            # Single optimized query with all required data
            sql = f"""
                SELECT {self.optimizer.ORDER_BASE_FIELDS},
                       {self.optimizer.ORDER_ITEM_FIELDS},
                       {self.optimizer.ADDRESS_FIELDS},
                       {self.optimizer.PAYMENT_FIELDS}
                FROM orders o
                LEFT JOIN order_items oi ON oi.order_id = o.id
                LEFT JOIN order_addresses oa ON oa.order_id = o.id
                LEFT JOIN payment_details pd ON pd.order_id = o.id
                WHERE o.order_id = :order_id
                ORDER BY oi.created_at ASC, pd.created_at DESC
            """
            
            rows = execute_raw_sql_readonly(sql, {'order_id': order_id})
            if not rows:
                return None

            # Process results efficiently
            header = rows[0]
            address = None
            items = []
            payments = []
            seen_items = set()
            seen_payments = set()

            for record in rows:
                # Build address once
                if address is None:
                    address = self.optimizer.build_address_dict(record)

                # Build items (avoid duplicates)
                item_id = record.get("item_id")
                if item_id and item_id not in seen_items:
                    items.append(self.optimizer.build_order_item_dict(record))
                    seen_items.add(item_id)

                # Build payments (avoid duplicates)
                payment_id = record.get("payment_id")
                if payment_id and payment_id not in seen_payments:
                    payments.append(self.optimizer.build_payment_dict(record))
                    seen_payments.add(payment_id)

            return {
                "id": header.get("id"),
                "order_id": header.get("order_id"),
                "customer_id": header.get("customer_id"),
                "customer_name": header.get("customer_name"),
                "facility_id": header.get("facility_id"),
                "facility_name": header.get("facility_name"),
                "status": OrderStatus.get_customer_status_name(header.get("status")),
                "total_amount": self.optimizer.safe_float_convert(header.get("total_amount")),
                "eta": header.get("eta"),
                "created_at": header.get("created_at"),
                "updated_at": header.get("updated_at"),
                "address": address,
                "items": items,
                "payments": payments
            }

        except Exception as e:
            logger.error(f"Error fetching order {order_id}: {e}")
            raise

    def get_orders_with_pagination_optimized(
        self, 
        user_id: str, 
        limit: int = 20, 
        offset: int = 0, 
        sort_order: str = "desc",
        include_items: bool = False
    ) -> Tuple[List[Dict], int]:
        """
        Get orders with optimized pagination and optional item inclusion.
        
        Returns tuple of (orders, total_count) for efficient pagination.
        """
        try:
            # Get total count efficiently
            count_sql = """
                SELECT COUNT(*) as total_count
                FROM orders o
                WHERE o.customer_id = :user_id
            """
            
            count_result = execute_raw_sql_readonly(count_sql, {'user_id': user_id})
            total_count = count_result[0].get('total_count', 0) if count_result else 0
            
            # Get orders with optimized query
            order_clause = self.optimizer.get_order_sort_clause(sort_order)
            
            if include_items:
                # Include items in single query for better performance
                sql = f"""
                    SELECT {self.optimizer.ORDER_BASE_FIELDS},
                           {self.optimizer.ORDER_ITEM_FIELDS}
                    FROM orders o
                    LEFT JOIN order_items oi ON oi.order_id = o.id
                    WHERE o.customer_id = :user_id
                    {order_clause}
                    LIMIT :limit OFFSET :offset
                """
            else:
                # Orders only for faster response
                sql = f"""
                    SELECT {self.optimizer.ORDER_BASE_FIELDS}
                    FROM orders o
                    WHERE o.customer_id = :user_id
                    {order_clause}
                    LIMIT :limit OFFSET :offset
                """
            
            rows = execute_raw_sql_readonly(sql, {
                'user_id': user_id,
                'limit': limit,
                'offset': offset
            })
            
            if include_items:
                # Process orders with items
                orders_dict = {}
                for row in rows:
                    order_id = row.get("order_id")
                    if order_id not in orders_dict:
                        orders_dict[order_id] = {
                            "id": row.get("id"),
                            "order_id": order_id,
                            "customer_id": row.get("customer_id"),
                            "customer_name": row.get("customer_name"),
                            "facility_id": row.get("facility_id"),
                            "facility_name": row.get("facility_name"),
                            "status": OrderStatus.get_customer_status_name(row.get("status")),
                            "total_amount": self.optimizer.safe_float_convert(row.get("total_amount")),
                            "eta": row.get("eta"),
                            "created_at": row.get("created_at"),
                            "updated_at": row.get("updated_at"),
                            "items": []
                        }
                    
                    # Add item if exists
                    if row.get("item_id"):
                        orders_dict[order_id]["items"].append(
                            self.optimizer.build_order_item_dict(row)
                        )
                
                orders = list(orders_dict.values())
            else:
                # Process orders only
                orders = []
                for row in rows:
                    orders.append({
                        "id": row.get("id"),
                        "order_id": row.get("order_id"),
                        "customer_id": row.get("customer_id"),
                        "customer_name": row.get("customer_name"),
                        "facility_id": row.get("facility_id"),
                        "facility_name": row.get("facility_name"),
                        "status": OrderStatus.get_customer_status_name(row.get("status")),
                        "total_amount": self.optimizer.safe_float_convert(row.get("total_amount")),
                        "eta": row.get("eta"),
                        "created_at": row.get("created_at"),
                        "updated_at": row.get("updated_at")
                    })
            
            return orders, total_count
            
        except Exception as e:
            logger.error(f"Error fetching orders for user {user_id}: {e}")
            raise

    def get_order_again_products_optimized(self, user_id: str, limit: int = 20, offset: int = 0) -> List[str]:
        """
        Optimized version of get_order_again_products with better performance.
        """
        try:
            # Optimized query for frequently ordered products
            sql = """
                SELECT oi.sku, COUNT(*) AS order_count
                FROM orders o
                INNER JOIN order_items oi ON oi.order_id = o.id
                WHERE o.customer_id = :user_id
                  AND o.status IN ({rozana_statuses})
                GROUP BY oi.sku
                ORDER BY order_count DESC, oi.sku ASC
                LIMIT :limit OFFSET :offset
            """.format(rozana_statuses=self.optimizer._ROZANA_STATUS_SQL)
            
            rows = execute_raw_sql_readonly(sql, {
                "user_id": user_id, 
                "limit": limit, 
                "offset": offset
            })
            
            return [row.get("sku") for row in rows if row.get("sku")]
            
        except Exception as e:
            logger.error(f"Error fetching order-again products for user {user_id}: {e}")
            return []

    def get_orders_by_status_optimized(
        self, 
        status_codes: List[int], 
        limit: int = 50, 
        offset: int = 0
    ) -> List[Dict]:
        """
        Get orders by specific status codes with optimized performance.
        """
        try:
            if not status_codes:
                return []
            
            status_sql = ', '.join(map(str, status_codes))
            order_clause = self.optimizer.get_order_sort_clause("desc")
            
            sql = f"""
                SELECT {self.optimizer.ORDER_BASE_FIELDS}
                FROM orders o
                WHERE o.status IN ({status_sql})
                {order_clause}
                LIMIT :limit OFFSET :offset
            """
            
            rows = execute_raw_sql_readonly(sql, {'limit': limit, 'offset': offset})
            
            orders = []
            for row in rows:
                orders.append({
                    "id": row.get("id"),
                    "order_id": row.get("order_id"),
                    "customer_id": row.get("customer_id"),
                    "customer_name": row.get("customer_name"),
                    "facility_id": row.get("facility_id"),
                    "facility_name": row.get("facility_name"),
                    "status": OrderStatus.get_customer_status_name(row.get("status")),
                    "total_amount": self.optimizer.safe_float_convert(row.get("total_amount")),
                    "eta": row.get("eta"),
                    "created_at": row.get("created_at"),
                    "updated_at": row.get("updated_at")
                })
            
            return orders
            
        except Exception as e:
            logger.error(f"Error fetching orders by status {status_codes}: {e}")
            raise
